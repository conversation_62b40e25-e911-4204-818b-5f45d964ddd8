<template>
	<view class="welcomeView">
		<view class="welcome_top">
			<view class="nameView">
				<image  src="@/static/login/logo.png" mode=""></image>
				
			</view>
			
			<text class="textWelcome">燃气安全管家</text>
			
			
		</view>
		
		<view class="top-bg" style="margin-top: 100rpx;"></view>
		
		
		
		<view class="loadView" v-if="userToken">
			<u-loading-icon mode="circle" size="30" color="#0165FC"></u-loading-icon>
		</view>
		
		
		<view class="welcome_bottom" v-else>
			<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelAdd" :rules="addRules" ref="addForm" labelWidth="90" :labelStyle="labelStyle">
				
				<u-form-item  style="text-align: right;"  label="用户头像" prop="userInfo.avatarUrl" borderBottom :required="true" >
					<button open-type="chooseAvatar" class="avatar" @chooseavatar="onChooseAvatar" >
					<image  :src='modelAdd.userInfo.avatarUrl' style="height: 150rpx;width: 100rpx;margin-left: 250rpx;border-radius: 20rpx;"  mode="widthFix"></image>
					<u-icon slot="right" name="arrow-right"></u-icon>	
					</button>
					
					
				</u-form-item>
				
				                  
				<u-form-item  label="用户昵称" prop="userInfo.nciname" borderBottom :required="true" @click="getName()" >
					
					<u--input :value="modelAdd.userInfo.userName" style="font-size: 35rpx;"    v-model="modelAdd.userInfo.userName" border="none" inputAlign="right"  type="nickname" class="weui-input" name="nickname" placeholder="请输入昵称"/>
					
					<u-icon slot="right" name="arrow-right"></u-icon>	
				</u-form-item>
				<u-form-item  label="用户类型" prop="userInfo.type" borderBottom :required="true" >
					<view class="checkbox">
                 <view class="paymode">
					<!-- <select-lay   class="seclectpaytype" :value="paytype" slabel="type" svalue="typeid" placeholder="普通用户"	 :options="paymode" @selectitem="selectitem"></select-lay> -->
					<ChoiceSelected class="select" :choice-content="choiceContent" :choice-index="choiceIndex" :choice-list="choiceList" @onChoiceClick="onChoiceClick"></ChoiceSelected>
				 </view>

					</view>
				</u-form-item>
				
				
				
				
			</u--form>
			
			
			

			</view>
			
			
			
			
				<xc-privacyPopup ref="privacyComponent" position="center" @allowPrivacy="allowPrivacy"></xc-privacyPopup>
				
		</view>
		
		
		<view class="buttonView">
			<button v-if="modelAdd.userInfo.userName != '' && modelAdd.userInfo.UserType != '' && modelAdd.userInfo.avatarUrl != '../../static/image/用户头像 (1).png'" 
			type="primary" text="登录" open-type="getPhoneNumber" 
		  class="oneBtn"  @getphonenumber="getPhoneNumber"
			@click="submit">登  录</button>
			
			<!-- <button type="primary" text="登录" open-type="getPhoneNumber" 
			class="oneBtn"  @getphonenumber="getPhoneNumber"
						@click="submit">登  录</button> -->
		</view>	
	</view>
</template>

<script>
	// 1. 导入组件
		import navbar from '@/components/navbar/navbar.vue';
		import xcPrivacyPopupVue from '../../components/xc-privacyPopup/xc-privacyPopup.vue';
		import ChoiceSelectedVue from '../../components/ChoiceSelected/ChoiceSelected.vue';
	import {
		toApplet
	} from "@/network/api.js"
	import {
		baseURL,
		baseURL_a
	} from '@/network/base.js';
	export default {
		components: { navbar , xcPrivacyPopupVue , ChoiceSelectedVue}, // 2. 注册组件
		data() {
			return {
				login: {
				      show: false,
				      avatar: '@/static/homePage/userIcon.png',
				    },
				avatarUrlLL: "https://www.ahhsiot.com/api/file/image?name=userIcon.png",
				modelAdd: {
					userInfo: {
					        avatarUrl: "../../static/image/用户头像 (1).png",
					        userName: "",
							UserType: '普通用户',
					      },
				},
				test: '',
				paymode: [
					{
					    type: '普通用户',
					    typeid: 1
					},
				    {
				    type: '运维人员',
				    typeid: 2
				},{
				    type: '管理员',
				    typeid: 3
				}
				],
				choiceList: [{
				                        choiceItemId: "0",
				                        choiceItemContent: "普通用户"
				                    },
				                    {
				                        choiceItemId: "1",
				                        choiceItemContent: "运维人员"
				                    },
				                    {
				                        choiceItemId: "2",
				                        choiceItemContent: "管理员"
				                    },
				                  
				                    
				                ],
				
				choiceContent: "普通用户",
				choiceIndex: 0,
				addRules: {
					
					'userInfo.ywName': {
						type: 'string',
						required: true,
						message: '请输入真实姓名',
						trigger: ['blur', 'change']
					},
					'userInfo.ywMobile': {
						type: 'string',
						required: true,
						message: '请输入手机号码',
						trigger: ['blur', 'change']
					}
					
				},
				paytype: '',
				payChannelid: '',
				isSelect:false,//展示类型？
				types:['类型一','类型二'],//公司/商户类型
				type:"",//公司/商户类型
				userToken: true,
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '44rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx',
					marginBottom: '50rpx'
				},
				wxBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#04BE02',
					borderRadius: '44rpx',
					fontSize: '28rpx',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				infoBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#FFFFFF',
					borderRadius: '44rpx',
					border: '2rpx solid #C1DFFF',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#333333',
					lineHeight: '28rpx'
				},
				userSign: 'pt'
			};
		},

		onLoad() {
			uni.setStorageSync("avatarUrl", this.modelAdd.userInfo.avatarUrl);
			console.log(this.modelAdd.userInfo,'this.modelAdd.userInfo')
			let tokenVal = uni.getStorageSync('token');
			if (tokenVal === "" || tokenVal == null) {
				this.userToken = false;
				
				// 查询隐私协议
							wx.getPrivacySetting({
								success: res => {
									// if (!res.needAuthorization) {
									// 	this.$refs.privacyComponent.closePrivacy();
									// 	// 查询授权，针对有tab切换的页面，可以在onshow中查询隐私授权状态，判断在tab切换后是否需要关闭授权弹框
									// 	console.log('已经同意隐私授权，不需要再次授权')
									// }
								},
								fail: () => {},
								complete: () => {
									console.log('已经同意隐私授权，不需要再次授权')
								}
							})
			} else {
				setTimeout(() => {
					uni.switchTab({
						url: "/pages/homePage/homePage"
					})
				}, 1000)
			}
			
			
			
			
			
		},
		methods: {
			goLogin() {
				uni.navigateTo({
					url: './login'
				})
			},
			onChoiceClick(e)
			{
				console.log('选中',e)
				this.choiceIndex = e
				this.choiceContent = this.choiceList[e].choiceItemContent
				this.modelAdd.userInfo.UserType = this.choiceContent
				console.log('this.choiceIndex',this.choiceIndex)
				console.log('this.choiceContent',this.choiceContent)
			},
			moreChange(e)
			{
				console.log('选中',e.target.value)
			},
			 getName(e){  
				 console.log(e,'e')
				 
			        this.modelAdd.userInfo.userName = e.detail.value;  
					console.log(this.modelAdd.userInfo,'this.modelAdd.userInfo')
			    },  
			submit() {
				uni.setStorageSync("nkiname", this.modelAdd.userInfo.userName);
				if(this.modelAdd.userInfo.UserType == '普通用户')
				{
				this.userSign = 'pt';
				}
				else if(this.modelAdd.userInfo.UserType == '运维人员')
				{
				this.userSign = 'yw';
				}
				else if(this.modelAdd.userInfo.UserType == '管理员')
				{
				this.userSign = 'admin';
				}
			},
			
			goRegister() {
				uni.navigateTo({
					url: './register'
				})
			},
			 onChooseAvatar(e){
				
				console.log(e.detail)
				 let result =  this.uploadFilePromise(e.detail.avatarUrl)
				 // let optData = JSON.parse(result.data);
				 console.log("ii", this.test)
				uni.setStorageSync("avatarUrl", e.detail.avatarUrl);
				this.modelAdd.userInfo.avatarUrl = e.detail.avatarUrl
				console.log(this.modelAdd.userInfo.avatarUrl,'this.modelAdd.userInfo.avatarUrl')
			},
			uploadFilePromise(url) {
				return new Promise((resolve, reject) => {
					let a = uni.uploadFile({
						url: baseURL_a + '/file/picUpload', // 仅为示例，非真实的接口地址
						filePath: url,
						name: 'file',
						// formData: {
						// 	user: 'test'
						// },
						success: (res) => {
							console.log("i", res, typeof(res.data))
							this.test = JSON.parse(res.data).data;
							console.log("iii", this.test )
							setTimeout(() => {
								resolve(res.data)
							}, 1000)
						},
						fail: (res) => {
							console.log('upload fail', res)
						}
					});
				})
			},
			saveSign(sign) {
				this.userSign = sign;
			},
			CheckChange(item) {
				console.log(JSON.stringify(item));
			},
			selectitem(index, item) {
			        this.payChannelid = item.typeid;
			        
			        if (index >= 0) {
			            this.paytype = this.paymode[index].typeid;
						this.modelAdd.userInfo.UserType= this.paymode[index].type;
			
						console.log(this.modelAdd.userInfo,'this.modelAdd.userInfo')
			        } else {
			            this.paytype = ""
			        }
			    },
allowPrivacy() {
				// 同意隐私协议触发事件，有些接口需要同意授权后才能执行，比如获取手机号授权接口，可以在同意隐私协议后，再执行授权获取手机号接口，如果不需要可以不添加该方法
				console.log('同意隐私授权')
			},

 getUserInfo(e) {
    console.log('getUserProfile')
    // 推荐使用 wx.getUserProfile 获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
    // 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res);
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
      }
    })
  },
  wxLogin: function(e) {
    debugger
    console.log('wxLogin')
    console.log(e.detail.userInfo);
    this.setData({
      userInfo: e.detail.userInfo
    })
    if (e.detail.userInfo == undefined) {
      app.globalData.hasLogin = false;
      util.showErrorToast('微信登录失败');
      return;
    }
    
  },
  modalConfirm() {
	  
	  uni.login({
	  	provider: "weixin",
	  	success: (loginRes) => {
	  		console.log('loginRes', loginRes);
	  		toApplet({
	  			code: loginRes.code,
	  			phoneCode: e.detail.code, //用户登录凭证
	  			encrypData: e.detail.encryptedData, //用户登录凭证
	  			iv: e.detail.iv, //用户登录凭证
	  			userType: this.userSign,
				avatar: this.modelAdd.userInfo.avatarUrl,
				name: this.modelAdd.userInfo.userName
	  		}).then(res => {
	  			uni.setStorageSync("userType", this.userSign);
	  			uni.setStorageSync("token", res.data.token);
	  			uni.setStorageSync("userId", res.data.id);
	  			uni.setStorageSync("userPhone", res.data.mobile);
	  			uni.setStorageSync("aleamInfo", "1");
	  			uni.setStorageSync("dateTime", Date.now());
	  			uni.setStorageSync("userS", res.data.userType);
	  			// let params = {
	  			// 	code: loginRes.code,
	  			// 	userType: app.globalData.status,
	  			// 	mobile: res.mobile
	  			// }
	  			// uni.setStorageSync("msg", JSON.stringify(params));
	  			uni.switchTab({
	  				url: "/pages/homePage/homePage"
	  			})
	  		})
	  	},
	  });
	  },

			getPhoneNumber(e) {
				console.log("iiii", this.test)
							
					uni.login({
						provider: "weixin",
						success: (loginRes) => {
							console.log('loginRes', loginRes);
							toApplet({
								code: loginRes.code,
								phoneCode: e.detail.code, //用户登录凭证
								encrypData: e.detail.encryptedData, //用户登录凭证
								iv: e.detail.iv, //用户登录凭证
								userType: this.userSign,
								avatar: this.test,
								name: this.modelAdd.userInfo.userName
							}).then(res => {
								uni.setStorageSync("userType", this.userSign);
								uni.setStorageSync("token", res.data.token);
								uni.setStorageSync("userId", res.data.id);
								uni.setStorageSync("userPhone", res.data.mobile);
								uni.setStorageSync("aleamInfo", "1");
								uni.setStorageSync("dateTime", Date.now());
								uni.setStorageSync("userS", res.data.userType);
								// let params = {
								// 	code: loginRes.code,
								// 	userType: app.globalData.status,
								// 	mobile: res.mobile
								// }
								// uni.setStorageSync("msg", JSON.stringify(params));
								uni.switchTab({
									url: "/pages/homePage/homePage"
								})
							})
						},
					});

				
			}
		}
	}
</script>

<style lang="scss" >
page{background-color: #ecf5ff;}

	.welcomeView {
		width: 100%;
		
		padding-top: 20%;
	
		
/* 设置背景颜色 */
		// .top-bg {
		//   height: 260rpx;
		//   background-color: #fa3534;
		// }
		



.head-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-bottom: 1rpx solid #fbdbdc;
  padding-bottom: 20rpx;
}

.avatar {
  // margin-top: -80rpx;
  font-weight: inherit;
  display: flex;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0);
}

.avatar::after {
  border: none;
}

.head-img {
  width: 140rpx;
  height: 140rpx;
  overflow: hidden;
  border-radius: 50%;
  background-color: #fbdbdc;
}

.tip {
  font-size: 26rpx;
  color: gray;
  margin: 15rpx 0;
}

.checkbox {
	display: flex;
	flex-direction: column;
	padding-left: 40%;
}
.paymode 
{
	

// .select {
// 	padding-right: 0;
// }
}
.check_box {
	margin-bottom: 20rpx;
}
		.welcome_top {
			display: flex;
			flex-direction: column;
			align-items: center;

			.nameView {
				display: flex;
				align-items: center;
				padding-top: 30rpx;
				margin-bottom: 30rpx;

				image {
					width: 280rpx;
					height: 80rpx;
					// margin-right: 30rpx;
					margin-top: 80rpx;
				}

				
			}
			text {
				// width: 480rpx;
				font-size: 50rpx;
				font-family: Microsoft YaHei UI-Bold, Microsoft YaHei UI;
				font-weight: bold;
				color: #5E5E5E;
				line-height: 60rpx;
				text-align: center;
			}

			.backImg {
				width: 80%;
				height: 150rpx;
				margin-top: 20rpx;
			}
		}

		.loadView {
			width: 100%;
			height: 200rpx;
			// margin-top: 154rpx;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.welcome_bottom {
			 margin-top: 150rpx;

			
		}
		.buttonView {
			
		.oneBtn {
			position: fixed;
			bottom: 350rpx;
			margin-left: 170rpx;
			// border-radius:5%;
			height: 90rpx;
			width: 400rpx;
			

			justify-content: center;
			align-items: center;
			
					border-radius: 25px;
					// border: 3rpx solid #6699FF;
					font-size: 38rpx;
					

		}
		}
	}
</style>