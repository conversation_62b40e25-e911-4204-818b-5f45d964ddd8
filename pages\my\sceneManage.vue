<template>
	<!-- 场景管理 -->
	<view class="sceneManage">
		<view class="listView">
			<view class="listModule" v-for="(item,index) in sceneList" :key='index'>
				<view class="titleHead">
					<text>{{item.sceneName}}</text>
					<view class="operBtn" v-if="item.id!=1">
						<u-button type="primary" :plain="true" size="small" text="编辑"
							@click="toEditPage(item)"></u-button>
						<text class="btnJ"></text>
						<u-button type="error" :plain="true" size="small" text="删除" @click="toDeletJL(item)"></u-button>
					</view>
				</view>
				<view class="">
					<text>包含设备</text>
					<view class="devAllView">
						<text v-for="(dev,i) in item.deviceInfoList">{{dev.code}}</text>
					</view>
				</view>
				<view class="">
					<text>创建时间：{{item.createTime}}</text>
				</view>
			</view>
		</view>
		<view class="btnXFView">
			<u-button type="primary" text="新增场景" :disabled="disabled" :customStyle="primaryBtnCss"
				@click="submitScene"></u-button>
		</view>
		<u-modal :content="delContent" title="提示" confirmText="确定" showCancelButton :show="delShowModal"
			@confirm="delConfirm" @cancel="delShowModal=false" style="text-align: center;"></u-modal>
	</view>
</template>

<script>
	import {
		sceneList,
		sceneUpdate
	} from "@/network/api.js"
	export default {
		data() {
			return {
				showQuerySheet: false,
				delContent: '是否删除?',
				delShowModal: false,
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				disabled: false,
				sceneList: [], //数据
				total: null, //总条数
				pageNum: 1, //第几页
				pageSize: 15, //显示多少条
				currentList: {}
			};
		},
		onShow() {
			console.log('jinlaile')
			this.pageNum = 1;
			this.sceneList = [];
			this.getList()
		},
		methods: {
			getList() { //获取数据
				let query = {
					userType: uni.getStorageSync('userType'),
					id: uni.getStorageSync('userId'),
					// pageNum: this.pageNum,
					// pageSize: this.pageSize
					// sceneId: id
				}
				sceneList(query).then(res => {
					console.log(res,'res')
					// if (res.data.length) {
					// 	this.sceneList = [...this.sceneList, ...res.data]
					// }
					this.sceneList = res.data
					this.total = res.total
				})
			},
			toEditPage(item) {
				uni.navigateTo({
					url: './sceneOperation?params=' + JSON.stringify(item)
				})
			},
			toDeletJL(item) {
				this.delShowModal = true;
				this.currentList = item
			},
			delConfirm() {
				this.delShowModal = false
				let form = {
					id: this.currentList.id,
					sceneName: this.currentList.sceneName,
					isDeleted: "Y" //删除标志 Y删除  N正常
				}
				sceneUpdate(form).then(res => {
						uni.showToast({
							title: '已删除',
							icon: "none"
						});
						this.pageNum = 1;
						this.sceneList = [];
						this.getList()
					})
					.catch((err) => {})
			},
			submitScene() {
				uni.navigateTo({
					url: './sceneOperation'
				})
			}
		},
		onReachBottom() { //触底事件
			if (this.pageNum * this.pageSize >= this.total) {
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none',
					duration: 1000
				});
			} else {
				if (this.pageNum <= this.pageNum - 1) {
					setTimeout(() => {
						uni.hideLoading()
					}, 500)
				} else {
					uni.showLoading({
						title: '加载中'
					});
					this.pageNum++
					this.getList()
				}
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
			}
		},
		onReady() {},
	};
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>
	.sceneManage {
		.listView {
			margin-top: 10rpx;
			margin-bottom: 200rpx;

			.listModule {
				background-color: #ffffff;
				margin-bottom: 12rpx;
				padding: 26rpx 40rpx;
				font-size: 24rpx;
				color: #3D3D3D;
				line-height: 48rpx;

				.titleHead {
					font-size: 34rpx;
					color: #409EFF;
					line-height: 56rpx;
					margin-bottom: 6rpx;
					display: flex;
					justify-content: space-between;

					.operBtn {
						display: flex;

						.btnJ {
							margin-right: 30rpx;
						}
					}
				}

				.devAllView {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					margin: 10rpx 0;

					text {
						width: 49%;
						background: #F3F3F3;
						border-radius: 10rpx;
						text-align: center;
						font-size: 24rpx;
						color: #3D3D3D;
						line-height: 50rpx;
						margin: 10rpx 0;
					}
				}
			}
		}
	}
</style>