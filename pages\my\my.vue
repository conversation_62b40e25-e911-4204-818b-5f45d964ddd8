<template>
	<view class="minePage">
		<view class="head-top">
			<view class="headLeft">
				<image :src="usermessage.avatar" mode=""></image>
			</view>
			<view class="headRight">
				<p class="hangA">{{userForm.nkiname}}
					<!-- <span>（手机号：{{userForm.phoneNumber}}）</span> -->
				</p>
				<p class="hangB">ID：{{userForm.userId}}</p>
			</view>
		</view>
		<view class="my-list-view">
			<u-cell-group>
				<u-cell title="用户类型" :value="userForm.userName"></u-cell>
				<u-cell title="手机号码" :value="userForm.phoneNumber"></u-cell>
				<u-cell title="版本号" value="v2.0.0"></u-cell>
				<u-cell title="联系热线" value="0551-63893271(转8617)"></u-cell>
				<u-cell title="场景管理" isLink url="sceneManage" v-if="userSign=='pt'"></u-cell>
				<!-- <u-cell title="用户管理" isLink url="userManage"></u-cell> -->
				<!-- <u-cell title="设备管理" isLink url="/pages/homePage/deviceManage"></u-cell> -->
				
				<!-- <u-cell title="修改密码" isLink url="changePwd"></u-cell> -->
				<u-cell title="退出登录" isLink @click="toLogOut"></u-cell>
			</u-cell-group>
		</view>
	</view>
</template>

<script>
	import {
		FindUserdetailByUids,
		AppletUser
	} from "@/network/api.js"
	export default {
		data() {
			return {
				userForm: {
					userName: uni.getStorageSync('userType')=='pt'?'普通用户':'运维人员',
					nkiname: uni.getStorageSync('nkiname'),
					phoneNumber: uni.getStorageSync('userPhone'),
					userId: uni.getStorageSync('userId'),
					avatarUrl: uni.getStorageSync('avatarUrl'),
				},
				userSign:'',
				usermessage: '',
			}
		},
		onLoad() {
			console.log(uni.getStorageSync('avatarUrl'),'avatarUrl')
			this.getUserData()
			this.userSign = uni.getStorageSync('userType');
		},
		methods: {
			getUserData() {
				 
				let id =  uni.getStorageSync('userId')
				
				console.log(id ,'用户详情parm')
				AppletUser(id).then(res => {
					console.log(res,'用户详情')
					this.usermessage = res.data
					console.log(this.usermessage,'用户详情11')
				})
				.catch(errors => {
					
				})
			},
			toLogOut() {
				uni.clearStorage();
				uni.reLaunch({
					url: `/pages/loginRegister/welcomePage`,
				});
			},
			goDetail() {
				uni.navigateTo({
					url: './userInform?params=' + JSON.stringify(this.userForm)
				})
			}
		},
	}
</script>
<style lang="scss" scoped>
	.minePage {
		// padding: 80rpx 40rpx;
		padding: 20rpx 40rpx 60rpx;

		.head-top {
			width: 670rpx;
			// height: 160rpx;
			background: #EFF6FF;
			border-radius: 8rpx 8rpx 8rpx 8rpx;
			display: flex;
			align-items: center;

			.headLeft {
				margin: 20rpx 50rpx;
				

				image {
					width: 116rpx;
					height: 114rpx;
					border-radius: 20rpx
				}
			}

			.headRight {
				.hangA {
					font-size: 28rpx;
					font-family: Microsoft YaHei UI-Bold, Microsoft YaHei UI;
					font-weight: bold;
					color: #222426;
					line-height: 28rpx;
					margin-bottom: 24rpx;

					span {
						font-weight: 200;
					}
				}

				.hangB {
					font-size: 24rpx;
					font-family: Microsoft YaHei UI-Regular, Microsoft YaHei UI;
					font-weight: 400;
					color: #636364;
					line-height: 28rpx;
				}
			}
		}

		.my-list-view {
			margin-top: 20rpx;
		}
	}
</style>