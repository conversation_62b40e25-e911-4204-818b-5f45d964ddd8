<template>
	<!-- 用户编辑 -->
	<view class="userEditPage">
		<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" :rules="patientRules" ref="patientForm" labelWidth="120"
				:labelStyle="labelStyle">
				<u-form-item label="用户名称" prop="userInfo.name" borderBottom :required="true">
					<u--input v-model="modelPatient.userInfo.name" maxlength="20" inputAlign="right" border="none"
						placeholder="请输入用户名称"></u--input>
				</u-form-item>
				<u-form-item label="电话" prop="userInfo.mobile"  borderBottom :required="true">
					<u--input v-model="modelPatient.userInfo.mobile" maxlength="30" inputAlign="right" border="none"
						placeholder="请输入电话"></u--input>
				</u-form-item>
			</u--form>
			<view class="btnXFView">
				<u-button type="primary" text="保存" :disabled="disabled" :customStyle="primaryBtnCss"
					@click="submit"></u-button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		AppletUserEdit
	} from "@/network/api.js"
	export default {
		data() {
			return {
				primaryBtnCss: {
					width: '574rpx',
					height: '88rpx',
					background: '#0165FC',
					boxShadow: '0rpx 4rpx 24rpx 0rpx rgba(54,150,255,0.4)',
					borderRadius: '200rpx',
					fontSize: '28rpx',
					fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					color: '#FFFFFF',
					lineHeight: '28rpx'
				},
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				disabled: false,
				modelPatient: {
					userInfo: {
						name: '',
						mobile: ''
					},
				},
				patientRules: {
					'userInfo.name': {
						type: 'string',
						required: true,
						message: '请输入',
						trigger: ['blur', 'change']
					},
					'userInfo.mobile': [{
							type: 'string',
							required: true,
							message: '请输入手机号',
							trigger: ['blur', 'change']
						},
						{
							// 自定义验证函数，见上说明
							validator: (rule, value, callback) => {
								// uni.$u.test.mobile()就是返回true或者false的
								return uni.$u.test.mobile(value);
							},
							message: '手机号码不正确',
							// 触发器可以同时用blur和change
							trigger: ['change', 'blur'],
						}
					],
				}
			};
		},
		onLoad(opt) {
			
			let paramsData = JSON.parse(opt.params)
			console.log('opt',paramsData)
			this.modelPatient.userInfo = paramsData;
		},
		methods: {
			submit() {
				this.$refs.patientForm.validate().then(res => {
					uni.showLoading({
						title: '请求中..'
					})
					let form = {
						id: this.modelPatient.userInfo.id,
						name: this.modelPatient.userInfo.name,
						mobile: this.modelPatient.userInfo.mobile
					}
					AppletUserEdit(form).then(res => {
							uni.navigateBack({
								delta: 1 // 默认值是1，表示返回的页面层数
							});
							uni.hideLoading();
						})
						.catch((err) => {
							uni.hideLoading();
						})
				}).catch(errors => {
					// uni.$u.toast('校验失败')
				})
			},
		},
		onReady() {
			//如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
			this.$refs.patientForm.setRules(this.patientRules)
		},
	};
</script>

<style lang="scss" scoped>
	.userEditPage {
		padding: 0 0 100rpx;
	}
</style>