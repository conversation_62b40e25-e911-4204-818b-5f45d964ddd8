<template>
	<!-- 用户管理 -->
	<view class="sceneManage">
		<view class="listView">
			<view class="listModule" v-for="(item,index) in userList" :key='index'>
				<view class="userHead">
					<image :src="item.picture" mode="" v-if="item.picture!=null&&item.picture!=''">
					</image>
					<image src="@/static/homePage/userIcon.png" mode="" v-else></image>
				</view>
				<view class="userContent">
					<view class="titleHead">
						<text>{{item.name}}</text>
						<view class="operBtn">
							<u-button type="primary" :plain="true" size="small" text="编辑"
								@click="toEditPage(item)"></u-button>
							<text class="btnJ"></text>
							<u-button type="error" :plain="true" size="small" text="删除"
								@click="toDeletJL(item)"></u-button>
						</view>
					</view>
					<view class="">
						<text>电话：{{item.mobile}}</text>
					</view>
					<view class="">
						<text>ID：{{item.id}}</text>
					</view>
					<view class="">
						<text>创建时间：{{item.createTime}}</text>
					</view>
				</view>
			</view>
		</view>
		<u-modal :content="delContent" title="提示" confirmText="确定" showCancelButton :show="delShowModal"
			@confirm="delConfirm" @cancel="delShowModal=false" style="text-align: center;"></u-modal>
	</view>
</template>

<script>
	import {
		appletUserList,
		AppletUserEdit
	} from "@/network/api.js"
	export default {
		data() {
			return {
				showQuerySheet: false,
				delContent: '是否删除',
				delShowModal: false,
				userList: [], //数据
				total: null, //总条数
				pageNum: 1, //第几页
				pageSize: 15 //显示多少条
			};
		},
		onShow() {
			this.pageNum = 1;
			this.userList = [];
			console.log('1')
			this.getList()
		},
		methods: {
			getList() { //获取数据
				let params = {
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}
				appletUserList(params).then(res => {
					// if (res.rows) {
						this.userList = [...this.userList, ...res.data.list]
					// }
					console.log('2')
					// this.userList = res.data.list
					console.log(this.userList,'3')
					this.total = res.data.total
				})
			},
			toEditPage(item) {
				uni.navigateTo({
					url: './userEdit?params=' + JSON.stringify(item)
				})
			},
			toDeletJL(item) {
				this.delShowModal = true;
				this.currentList = item
			},
			delConfirm() {
				this.delShowModal = false
				let form = {
					id: this.currentList.id,
					isDeleted: "Y" //删除标志 Y删除  N正常
				}
				AppletUserEdit(form).then(res => {
						uni.showToast({
							title: '已删除',
							icon: "none"
						});
						this.pageNum = 1;
						this.userList = [];
						this.getList()
					})
					.catch((err) => {})
			},
		},
		onReachBottom() { //触底事件
			if (this.pageNum * this.pageSize >= this.total) {
				uni.showToast({
					title: '没有更多数据了',
					icon: 'none',
					duration: 1000
				});
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
			} else {
				if (this.pageNum <= this.pageNum - 1) {
					setTimeout(() => {
						uni.hideLoading()
					}, 500)
				} else {
					uni.showLoading({
						title: '加载中'
					});
					this.pageNum++
					this.getList()
				}
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
			}
		},
		onReady() {},
	};
</script>
<style>
	page {
		background-color: #F6F6F6;
	}
</style>
<style lang="scss" scoped>
	.sceneManage {
		.listView {
			margin-top: 10rpx;

			.listModule {
				background-color: #ffffff;
				margin-bottom: 12rpx;
				padding: 26rpx 40rpx;
				font-size: 24rpx;
				color: #3D3D3D;
				line-height: 38rpx;
				display: flex;
				align-items: center;

				.userHead {
					margin-right: 44rpx;

					image {
						width: 86rpx;
						height: 86rpx;
					}
				}

				.userContent {
					flex: 1;
				}

				.titleHead {
					font-size: 34rpx;
					color: #409EFF;
					line-height: 56rpx;
					margin-bottom: 6rpx;
					display: flex;
					justify-content: space-between;

					.operBtn {
						display: flex;

						.btnJ {
							margin-right: 30rpx;
						}
					}
				}

				.devAllView {
					display: flex;
					flex-wrap: wrap;
					justify-content: space-between;
					margin: 10rpx 0;

					text {
						width: 49%;
						background: #F3F3F3;
						border-radius: 10rpx;
						text-align: center;
						font-size: 24rpx;
						color: #3D3D3D;
						line-height: 50rpx;
						margin: 10rpx 0;
					}
				}
			}
		}
	}
</style>