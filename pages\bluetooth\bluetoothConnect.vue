<template>
	<view class="bluetooth-page">
		<view class="container">
			<view class="status-card">
				<view class="status-text" :class="[bluetoothStatus.class]">{{bluetoothStatus.text}}</view>
				<view class="scan-indicator" v-if="isScanning">
					<view class="loader"></view>
					<text>正在努力扫描中...</text>
				</view>
				
				<button class="btn btn-primary" @click="startScan" v-if="!isConnected">
					{{ isScanning ? '停止扫描' : '扫描蓝牙设备' }}
				</button>
				<button class="btn btn-secondary" @click="disconnectDevice" v-if="isConnected">
					断开当前设备
				</button>
			</view>
			
			<view class="device-list" v-if="showDeviceList && deviceList.length > 0">
				<view class="card-title">可用设备</view>
				<view class="device-item" v-for="(device, index) in deviceList" :key="index" @click="connectToDevice(device)">
					<view class="device-info">
						<view class="device-name">{{device.name}}</view>
						<view class="device-mac">MAC: {{device.macAddress || device.deviceId}}</view>
						<view class="device-id">ID: {{device.deviceId}}</view>
					</view>
					<button class="btn btn-sm btn-primary connect-btn">连接</button>
				</view>
			</view>
			
			<view class="empty-devices" v-if="showDeviceList && deviceList.length === 0">
				<image src="@/static/image/暂无设备.png" class="empty-image"></image>
				<text class="empty-text">未发现可用设备，请确保设备已开启并靠近手机</text>
			</view>
			
			<button class="btn btn-success command-btn" v-if="isConnected" @click="goToCommandPage">
				为 {{currentDevice.name}} 配置指令
			</button>
			
			<button class="btn btn-primary test-btn" v-if="isConnected" @click="testDevice">
				设备测试
			</button>
			
			<button class="btn btn-secondary record-btn" @click="goToTestRecordPage">
				查看测试记录
			</button>
		</view>
		
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{toastMessage}}</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isScanning: false,
			isConnected: false,
			showDeviceList: false,
			deviceList: [],
			currentDevice: null,
			bluetoothStatus: {
				text: '点击下方按钮开始扫描',
				class: ''
			},
			showToast: false,
			toastMessage: '',
			toastType: 'default',
			isBluetoothAvailable: false,
			isBluetoothInit: false,
			discoveryTimeout: null,
			services: [],
			deviceServicesMap: new Map(),
			deviceCharacteristicsMap: new Map(),
			isConnecting: false,
			isDisconnecting: false
		}
	},
	onLoad() {
		// 检查蓝牙是否可用
		this.checkBluetoothAvailable();
	},
	onShow() {
		// 页面显示时检查蓝牙状态
		if (this.isBluetoothInit) {
			this.getBluetoothAdapterState();
		}
	},
	onUnload() {
		// 页面卸载时清理蓝牙资源
		this.cleanupBluetooth();
	},
	onHide() {
		// 页面隐藏时清理资源
		if (this.isScanning) {
			this.stopScan();
		}
	},
	methods: {
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;
			
			// 自动关闭
			setTimeout(() => {
				this.showToast = false;
			}, 2000);
			
			// 为了兼容性，同时使用uni.showToast
			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},
		checkBluetoothAvailable() {
			this.bluetoothStatus.text = '正在检查蓝牙可用性...';
			
			// #ifdef APP-PLUS
			// 检查位置权限
			console.log('检查位置服务是否开启...');
			var context = plus.android.importClass("android.content.Context");
			var locationManager = plus.android.importClass("android.location.LocationManager");
			var main = plus.android.runtimeMainActivity();
			var mainSvr = main.getSystemService(context.LOCATION_SERVICE);
			var result = mainSvr.isProviderEnabled(locationManager.GPS_PROVIDER);
			console.log("GPS状态:", result);
			
			if (!result) {
				this.showCustomToast({
					message: '请开启位置服务，蓝牙扫描需要定位权限',
					type: 'warning'
				});
			}
			// #endif
			
			uni.openBluetoothAdapter({
				success: (res) => {
					console.log('蓝牙适配器初始化成功', res);
					this.isBluetoothAvailable = true;
					this.isBluetoothInit = true;
					this.bluetoothStatus.text = '蓝牙已就绪，点击下方按钮开始扫描';
					
					// 监听蓝牙适配器状态变化
					this.onBluetoothAdapterStateChange();
					
					// 获取蓝牙适配器状态
					this.getBluetoothAdapterState();
				},
				fail: (err) => {
					console.error('蓝牙适配器初始化失败', err);
					this.isBluetoothAvailable = false;
					this.isBluetoothInit = false;
					
					if (err.errCode === 10001) {
						this.bluetoothStatus.text = '请检查设备蓝牙是否正常开启';
						this.bluetoothStatus.class = 'error';
					} else {
						this.bluetoothStatus.text = '蓝牙初始化失败: ' + err.errMsg;
						this.bluetoothStatus.class = 'error';
					}
					
					this.showCustomToast({
						message: '蓝牙不可用，请开启设备蓝牙',
						type: 'error'
					});
				}
			});
		},
		getBluetoothAdapterState() {
			uni.getBluetoothAdapterState({
				success: (res) => {
					console.log('蓝牙适配器状态:', res);
					
					if (res.available) {
						this.isBluetoothAvailable = true;
						
						if (res.discovering) {
							this.isScanning = true;
							this.bluetoothStatus.text = '正在扫描附近的蓝牙设备...';
							this.bluetoothStatus.class = 'scanning';
						} else {
							this.isScanning = false;
						}
					} else {
						this.isBluetoothAvailable = false;
						this.bluetoothStatus.text = '蓝牙不可用，请开启设备蓝牙';
						this.bluetoothStatus.class = 'error';
					}
				},
				fail: (err) => {
					console.error('获取蓝牙适配器状态失败', err);
					this.isBluetoothAvailable = false;
				}
			});
		},
		onBluetoothAdapterStateChange() {
			uni.onBluetoothAdapterStateChange((res) => {
				console.log('蓝牙适配器状态变化:', res);
				
				if (res.available) {
					this.isBluetoothAvailable = true;
					
					if (!this.isBluetoothInit) {
						this.isBluetoothInit = true;
						this.bluetoothStatus.text = '蓝牙已就绪，点击下方按钮开始扫描';
						this.bluetoothStatus.class = '';
					}
					
					if (res.discovering) {
						this.isScanning = true;
						this.bluetoothStatus.text = '正在扫描附近的蓝牙设备...';
						this.bluetoothStatus.class = 'scanning';
					} else if (this.isScanning) {
						this.isScanning = false;
						this.bluetoothStatus.text = '扫描已停止，点击按钮重新扫描';
						this.bluetoothStatus.class = '';
					}
				} else {
					this.isBluetoothAvailable = false;
					this.isBluetoothInit = false;
					this.bluetoothStatus.text = '蓝牙已关闭，请开启设备蓝牙';
					this.bluetoothStatus.class = 'error';
					
					if (this.isScanning) {
						this.isScanning = false;
						this.showDeviceList = false;
						this.deviceList = [];
					}
					
					if (this.isConnected) {
						this.isConnected = false;
						this.currentDevice = null;
						this.showCustomToast({
							message: '蓝牙已断开连接',
							type: 'error'
						});
					}
				}
			});
		},
		startScan() {
			if (!this.isBluetoothAvailable) {
				this.checkBluetoothAvailable();
				return;
			}
			
			if (this.isConnected) {
				this.showCustomToast({
					message: '已连接设备，请先断开后再扫描',
					type: 'warning'
				});
				return;
			}
			
			if (this.isScanning) {
				// 如果正在扫描，则停止扫描
				this.stopScan();
				return;
			}
			
			// 先确保之前的扫描已停止，并清理旧的监听器
			try {
				uni.offBluetoothDeviceFound();
				console.log('开始扫描前移除旧的蓝牙设备发现监听');
			} catch (error) {
				console.error('移除蓝牙设备发现监听失败:', error);
			}
			
			uni.stopBluetoothDevicesDiscovery({
				complete: () => {
					console.log('确保之前的扫描已停止');
					
					// 显示loading状态
					this.isScanning = true;
					this.showDeviceList = true;
					this.bluetoothStatus.text = '正在扫描附近的蓝牙设备...';
					this.bluetoothStatus.class = 'scanning';
					this.deviceList = []; // 清空之前的设备列表
					
					// 检查蓝牙权限
					// #ifdef APP-PLUS
					console.log('检查蓝牙权限...');
					var BTPermission = 'android.permission.BLUETOOTH';
					var BTAdminPermission = 'android.permission.BLUETOOTH_ADMIN';
					var BTConnectPermission = 'android.permission.BLUETOOTH_CONNECT';
					var BTScanPermission = 'android.permission.BLUETOOTH_SCAN';
					var locationPermission = 'android.permission.ACCESS_FINE_LOCATION';
					
					// 请求权限
					uni.getSystemInfo({
						success: (sysInfo) => {
							if (sysInfo.platform === 'android') {
								const androidVersion = parseInt(sysInfo.osVersion.split('.')[0]);
								console.log('Android版本:', androidVersion);
								
								// Android 12及以上需要BLUETOOTH_CONNECT和BLUETOOTH_SCAN权限
								if (androidVersion >= 12) {
									plus.android.requestPermissions(
										[BTConnectPermission, BTScanPermission, locationPermission],
										function(resultObj) {
											console.log('蓝牙权限请求结果:', resultObj);
										}
									);
								} else {
									// Android 12以下需要BLUETOOTH, BLUETOOTH_ADMIN和位置权限
									plus.android.requestPermissions(
										[BTPermission, BTAdminPermission, locationPermission],
										function(resultObj) {
											console.log('蓝牙权限请求结果:', resultObj);
										}
									);
								}
							}
						}
					});
					// #endif
					
					// 监听寻找到新设备的事件
					this.onBluetoothDeviceFound();
					
					// 开始搜寻附近的蓝牙设备
					uni.startBluetoothDevicesDiscovery({
						allowDuplicatesKey: true, // 允许重复上报同一设备，以防遗漏
						interval: 0, // 0 表示以最小间隔扫描
						powerLevel: 'high', // 高功率扫描
						success: (res) => {
							console.log('开始搜寻蓝牙设备:', res);
							
							// 获取在蓝牙模块生效期间所有已发现的蓝牙设备
							this.getBluetoothDevices();
							
							// 设置超时，防止一直扫描消耗电量
							this.discoveryTimeout = setTimeout(() => {
								// 如果还在扫描状态，则停止扫描
								if (this.isScanning) {
									this.stopScan();
									
									if (this.deviceList.length === 0) {
										this.showCustomToast({
											message: '未找到蓝牙设备，请确保设备已开启',
											type: 'warning'
										});
									}
								}
							}, 20000); // 延长到20秒后停止扫描
						},
						fail: (err) => {
							console.error('搜寻蓝牙设备失败:', err);
							this.isScanning = false;
							this.bluetoothStatus.text = '扫描失败: ' + err.errMsg;
							this.bluetoothStatus.class = 'error';
							
							this.showCustomToast({
								message: '扫描蓝牙设备失败，请重试',
								type: 'error'
							});
						}
					});
				}
			});
		},
		stopScan() {
			// 清除超时定时器
			if (this.discoveryTimeout) {
				clearTimeout(this.discoveryTimeout);
				this.discoveryTimeout = null;
			}
			
			// 确保移除蓝牙设备发现监听
			try {
				uni.offBluetoothDeviceFound();
				console.log('已移除蓝牙设备发现监听');
			} catch (error) {
				console.error('移除蓝牙设备发现监听失败:', error);
			}
			
			uni.stopBluetoothDevicesDiscovery({
				success: (res) => {
					console.log('停止搜寻蓝牙设备:', res);
					this.isScanning = false;
					this.bluetoothStatus.text = this.deviceList.length > 0 ? 
						'扫描完成，请选择需要连接的设备' : 
						'未发现可用设备，请确保设备已开启并在附近';
					this.bluetoothStatus.class = '';
				},
				fail: (err) => {
					console.error('停止搜寻蓝牙设备失败:', err);
				},
				complete: () => {
					this.isScanning = false;
				}
			});
		},
		getBluetoothDevices() {
			setTimeout(() => {
				uni.getBluetoothDevices({
					success: (res) => {
						console.log('获取蓝牙设备列表成功:', res);
						
						if (res.devices && res.devices.length > 0) {
							const filteredDevices = res.devices.filter(device => 
								device.name && 
								(device.name.includes('SEEFY') 
								// || 
								//  device.name.includes('报警') || 
								//  device.name.includes('K001') || 
								//  device.name.includes('alarm') || 
								//  device.name.toLowerCase().includes('gas')
								)
							);
							
							// 更新设备列表，过滤掉重复的设备
							const newList = [...this.deviceList];
							
							filteredDevices.forEach(device => {
								// 检查是否已存在于列表中
								const exists = newList.some(d => d.deviceId === device.deviceId);
								if (!exists) {
									// 获取MAC地址
									let macAddress = '';
									
									// #ifdef APP-PLUS
									try {
										// 尝试获取Android设备的MAC地址
										const deviceId = device.deviceId;
										if (plus.os.name === 'Android') {
											// Android平台上，deviceId通常就是蓝牙MAC地址
											macAddress = deviceId.replace(/:/g, ':').toUpperCase();
										} else if (plus.os.name === 'iOS') {
											// iOS下无法直接获取MAC，但可以显示UUID
											macAddress = deviceId;
										}
									} catch (e) {
										console.error('获取MAC地址失败:', e);
										macAddress = device.deviceId; // 如果无法获取MAC，则使用deviceId
									}
									// #endif
									
									// #ifndef APP-PLUS
									macAddress = device.deviceId; // 浏览器环境使用deviceId
									// #endif
									
									newList.push({
										name: device.name,
										deviceId: device.deviceId,
										RSSI: device.RSSI,
										advertisData: device.advertisData,
										macAddress: macAddress // 添加MAC地址字段
									});
								}
							});
							
							// 根据信号强度排序
							this.deviceList = newList.sort((a, b) => b.RSSI - a.RSSI);
							
							// 如果还在扫描中，继续更新设备列表
							if (this.isScanning) {
								setTimeout(() => {
									this.getBluetoothDevices();
								}, 2000);
							}
						}
					},
					fail: (err) => {
						console.error('获取蓝牙设备列表失败:', err);
					}
				});
			}, 1000); // 延迟1秒获取设备列表
		},
		onBluetoothDeviceFound() {
			// 务必先移除可能存在的旧监听器，防止重复
			try {
				uni.offBluetoothDeviceFound();
				console.log('清除现有蓝牙设备发现监听');
			} catch (error) {
				console.error('清除蓝牙设备发现监听失败:', error);
			}
			
			uni.onBluetoothDeviceFound((res) => {
				// 确保只在扫描状态下处理设备发现
				if (!this.isScanning) {
					console.log('非扫描状态，忽略设备发现');
					return;
				}
				
				// 在新设备发现时，不立即更新界面，而是收集设备
				if (res.devices && res.devices.length > 0) {
					res.devices.forEach(device => {
						// 放宽过滤条件，仅过滤掉没有名称的设备
						if (device.name && device.name.trim() !== '') {
							// 输出所有找到的设备，方便调试
							console.log(`新发现设备: ${device.name}, ID: ${device.deviceId}, RSSI: ${device.RSSI}`);
							
							// 获取MAC地址
							let macAddress = '';
							
							// #ifdef APP-PLUS
							try {
								const deviceId = device.deviceId;
								if (plus.os.name === 'Android') {
									// Android平台上，deviceId通常就是蓝牙MAC地址
									macAddress = deviceId.replace(/:/g, ':').toUpperCase();
								} else if (plus.os.name === 'iOS') {
									// iOS下无法直接获取MAC，但可以显示UUID
									macAddress = deviceId;
								}
							} catch (e) {
								console.error('获取MAC地址失败:', e);
								macAddress = device.deviceId; // 如果无法获取MAC，则使用deviceId
							}
							// #endif
							
							// #ifndef APP-PLUS
							macAddress = device.deviceId; // 浏览器环境使用deviceId
							// #endif
							
							// 检查是否已存在于列表中
							const exists = this.deviceList.some(d => d.deviceId === device.deviceId);
							if (!exists) {
								this.deviceList.push({
									name: device.name,
									deviceId: device.deviceId,
									RSSI: device.RSSI,
									advertisData: device.advertisData,
									macAddress: macAddress // 添加MAC地址字段
								});
							}
						}
					});
					
					// 根据信号强度排序
					this.deviceList.sort((a, b) => b.RSSI - a.RSSI);
				}
			});
		},
		connectToDevice(device) {
			if (this.isConnected) {
				this.showCustomToast({
					message: '已连接设备，如需连接新设备请先断开',
					type: 'warning'
				});
				return;
			}
			
			if (this.isConnecting) {
				return; // 防止重复点击
			}
			
			this.isConnecting = true;
			this.bluetoothStatus.text = `正在连接到 ${device.name}...`;
			this.bluetoothStatus.class = 'scanning';
			
			// 停止扫描前关闭发现设备监听器，确保彻底清理
			try {
				uni.offBluetoothDeviceFound();
				console.log('连接前移除蓝牙设备发现监听');
			} catch (error) {
				console.error('移除蓝牙设备发现监听失败:', error);
			}
			
			// 停止扫描
			uni.stopBluetoothDevicesDiscovery({
				success: (res) => {
					console.log('连接前停止搜寻蓝牙设备成功:', res);
					// 确保设置扫描状态为false
					this.isScanning = false;
					
					// 创建蓝牙连接
					this.createBLEConnection(device);
				},
				fail: (err) => {
					console.error('连接前停止搜寻蓝牙设备失败:', err);
					// 即使停止扫描失败，也尝试连接设备
					this.isScanning = false;
					this.createBLEConnection(device);
				}
			});
		},
		disconnectDevice() {
			if (!this.isConnected || !this.currentDevice) {
				return;
			}
			
			if (this.isDisconnecting) {
				return; // 防止重复点击
			}
			
			this.isDisconnecting = true;
			this.bluetoothStatus.text = `正在断开与 ${this.currentDevice.name} 的连接...`;
			
			uni.closeBLEConnection({
				deviceId: this.currentDevice.deviceId,
				success: (res) => {
					console.log('断开蓝牙设备成功:', res);
					
					// 清理设备信息
					this.currentDevice = null;
					this.services = [];
					this.deviceServicesMap.delete(this.currentDevice?.deviceId);
					this.deviceCharacteristicsMap.delete(this.currentDevice?.deviceId);
					
					// 更新UI状态
					this.isConnected = false;
					this.bluetoothStatus.text = '设备已断开。点击下方按钮重新扫描。';
					this.bluetoothStatus.class = '';
					this.showDeviceList = false;
					
					this.showCustomToast({
						message: '已断开蓝牙连接',
						type: 'default'
					});
				},
				fail: (err) => {
					console.error('断开蓝牙设备失败:', err);
					
					this.showCustomToast({
						message: '断开连接失败，请重试',
						type: 'error'
					});
				},
				complete: () => {
					this.isDisconnecting = false;
				}
			});
		},
		onBLEConnectionStateChange() {
			uni.onBLEConnectionStateChange((res) => {
				console.log('蓝牙连接状态变化:', res);
				
				// 连接意外断开
				if (!res.connected && this.isConnected && this.currentDevice && res.deviceId === this.currentDevice.deviceId) {
					console.log('蓝牙连接已断开');
					
					// 清理设备信息
					this.currentDevice = null;
					this.services = [];
					this.deviceServicesMap.delete(res.deviceId);
					this.deviceCharacteristicsMap.delete(res.deviceId);
					
					// 更新UI状态
					this.isConnected = false;
					this.bluetoothStatus.text = '设备连接已断开';
					this.bluetoothStatus.class = 'error';
					this.showDeviceList = false;
					
					this.showCustomToast({
						message: '蓝牙连接已断开',
						type: 'warning'
					});
				}
			});
		},
		getBLEDeviceServices(deviceId) {
			if (!deviceId) return;
			
			// 先清除之前可能存在的特征值信息
			uni.removeStorageSync('writeServiceId');
			uni.removeStorageSync('writeCharacteristicId');
			uni.removeStorageSync('notifyServiceId');
			uni.removeStorageSync('notifyCharacteristicId');
			
			setTimeout(() => {
				uni.getBLEDeviceServices({
					deviceId: deviceId,
					success: (res) => {
						console.log('获取蓝牙设备服务成功:', res.services);
						
						if (res.services && res.services.length > 0) {
							this.services = res.services;
							this.deviceServicesMap.set(deviceId, res.services);
							
							// 遍历所有服务，以确保不会遗漏
							res.services.forEach(service => {
								this.getBLEDeviceCharacteristics(deviceId, service.uuid);
							});
						}
					},
					fail: (err) => {
						console.error('获取蓝牙设备服务失败:', err);
					}
				});
			}, 1000); // 延迟1秒执行，等待连接稳定
		},
		getBLEDeviceCharacteristics(deviceId, serviceId) {
			if (!deviceId || !serviceId) return;
			
			uni.getBLEDeviceCharacteristics({
				deviceId: deviceId,
				serviceId: serviceId,
				success: (res) => {
					console.log(`获取服务(${serviceId})的特征值成功:`, res.characteristics);
					
					if (res.characteristics && res.characteristics.length > 0) {
						// 存储特征值信息
						if (!this.deviceCharacteristicsMap.has(deviceId)) {
							this.deviceCharacteristicsMap.set(deviceId, new Map());
						}
						this.deviceCharacteristicsMap.get(deviceId).set(serviceId, res.characteristics);
						
						// 查找支持读写通知的特征值
						// 查找可写入的特征
						const writeCharacteristics = res.characteristics.filter(
							char => char.properties.write || char.properties.writeNoResponse
						);
						
						// 查找支持通知的特征
						const notifyCharacteristics = res.characteristics.filter(
							char => char.properties.notify || char.properties.indicate
						);
						
						// 如果找到可写特征，优先保存，以防止被后续服务覆盖
						if (writeCharacteristics.length > 0 && !uni.getStorageSync('writeCharacteristicId')) {
							console.log(`找到可写特征值: 服务ID=${serviceId}, 特征值ID=${writeCharacteristics[0].uuid}`);
							uni.setStorageSync('writeServiceId', serviceId);
							uni.setStorageSync('writeCharacteristicId', writeCharacteristics[0].uuid);
						}
						
						// 启用通知
						if (notifyCharacteristics.length > 0 && !uni.getStorageSync('notifyCharacteristicId')) {
							console.log(`找到通知特征值: 服务ID=${serviceId}, 特征值ID=${notifyCharacteristics[0].uuid}`);
							uni.setStorageSync('notifyServiceId', serviceId);
							uni.setStorageSync('notifyCharacteristicId', notifyCharacteristics[0].uuid);
							this.setupCharacteristicNotifications(deviceId, serviceId, notifyCharacteristics[0].uuid);
						}
					}
				},
				fail: (err) => {
					console.error(`获取服务(${serviceId})的特征值失败:`, err);
				}
			});
		},
		setupCharacteristicNotifications(deviceId, serviceId, characteristicId) {
			// 启用通知
			uni.notifyBLECharacteristicValueChange({
				deviceId: deviceId,
				serviceId: serviceId,
				characteristicId: characteristicId,
				state: true, // 启用通知
				success: (res) => {
					console.log('启用特征值通知成功:', res);
					
					// 监听特征值变化
					this.onBLECharacteristicValueChange();
					
					// 保存通知特征值的UUID
					uni.setStorageSync('notifyCharacteristicId', characteristicId);
					uni.setStorageSync('notifyServiceId', serviceId);
				},
				fail: (err) => {
					console.error('启用特征值通知失败:', err);
				}
			});
		},
		onBLECharacteristicValueChange() {
			uni.onBLECharacteristicValueChange((res) => {
				console.log('收到特征值变化:', res);
				
				// 处理接收到的数据
				if (res.value) {
					const buffer = res.value;
					const dataView = new DataView(buffer);
					const uint8Array = new Uint8Array(buffer);
					
					// 打印十六进制数据
					let hexData = '';
					for (let i = 0; i < uint8Array.length; i++) {
						hexData += uint8Array[i].toString(16).padStart(2, '0') + ' ';
					}
					console.log('收到数据(HEX):', hexData);
					
					// 尝试解析数据
					try {
						// 判断第一个字节来确定命令类型
						const cmdType = dataView.getUint8(0);
						
						switch (cmdType) {
							case 0x01: // 网络参数配置响应
								if (uint8Array.length >= 2) {
									const status = dataView.getUint8(1);
									let message = '';
									
									if (status === 0x00) {
										message = '网络参数配置成功';
										this.showCustomToast({
											message: message,
											type: 'default'
										});
									} else {
										message = '网络参数配置失败，错误码: ' + status;
										this.showCustomToast({
											message: message,
											type: 'error'
										});
									}
									console.log('网络参数配置响应:', message);
								}
								break;
								
							case 0x02: // 设备状态上报
								if (uint8Array.length >= 4) {
									const deviceStatus = dataView.getUint8(1);
									const batteryLevel = dataView.getUint8(2);
									const signalStrength = dataView.getUint8(3);
									
									let statusText = '';
									switch (deviceStatus) {
										case 0x00: statusText = '正常'; break;
										case 0x01: statusText = '告警'; break;
										case 0x02: statusText = '故障'; break;
										default: statusText = '未知';
									}
									
									const message = `设备状态: ${statusText}, 电量: ${batteryLevel}%, 信号: ${signalStrength}%`;
									console.log(message);
									
									this.showCustomToast({
										message: message,
										type: deviceStatus === 0x00 ? 'default' : 'warning'
									});
								}
								break;
								
							case 0x03: // 气体浓度数据
								if (uint8Array.length >= 3) {
									const gasType = dataView.getUint8(1);
									const concentration = dataView.getUint8(2);
									
									let gasTypeName = '';
									switch (gasType) {
										case 0x01: gasTypeName = '天然气'; break;
										case 0x02: gasTypeName = '液化气'; break;
										case 0x03: gasTypeName = '一氧化碳'; break;
										default: gasTypeName = '未知气体';
									}
									
									const message = `${gasTypeName}浓度: ${concentration}%LEL`;
									console.log(message);
									
									// 浓度高于报警阈值时显示警告
									if (concentration >= 25) {
										this.showCustomToast({
											message: '警告! ' + message,
											type: 'error'
										});
									} else if (concentration >= 10) {
										this.showCustomToast({
											message: '注意! ' + message,
											type: 'warning'
										});
									}
								}
								break;
								
							default:
								// 尝试将数据解析为字符串
								try {
									const decoder = new TextDecoder('utf-8');
									const text = decoder.decode(buffer);
									console.log('收到数据(TEXT):', text);
									
									// 如果是JSON格式，尝试解析
									if (text.startsWith('{') && text.endsWith('}')) {
										try {
											const jsonData = JSON.parse(text);
											console.log('JSON数据:', jsonData);
										} catch (e) {
											console.error('JSON解析失败:', e);
										}
									}
								} catch (error) {
									console.error('字符串解码失败:', error);
								}
								break;
						}
					} catch (error) {
						console.error('数据解析失败:', error);
						
						// 尝试转换为字符串
						try {
							const decoder = new TextDecoder('utf-8');
							const text = decoder.decode(buffer);
							console.log('收到数据(TEXT):', text);
						} catch (error) {
							console.error('数据解码失败:', error);
						}
					}
				}
			});
		},
		// 读取低功耗蓝牙设备的特征值
		readBLECharacteristicValue(deviceId, serviceId, characteristicId) {
			if (!deviceId || !serviceId || !characteristicId) {
				console.error('读取特征值失败：参数不完整');
				return;
			}
			
			uni.readBLECharacteristicValue({
				deviceId: deviceId,
				serviceId: serviceId,
				characteristicId: characteristicId,
				success: (res) => {
					console.log('读取特征值成功:', res);
					// 特征值数据将通过onBLECharacteristicValueChange事件返回
				},
				fail: (err) => {
					console.error('读取特征值失败:', err);
					this.showCustomToast({
						message: '读取设备数据失败',
						type: 'error'
					});
				}
			});
		},
		cleanupBluetooth() {
			// 停止扫描
			if (this.isScanning) {
				uni.stopBluetoothDevicesDiscovery({
					complete: () => {
						console.log('停止搜寻蓝牙设备');
					}
				});
			}
			
			// 关闭蓝牙模块
			if (this.isBluetoothInit) {
				uni.closeBluetoothAdapter({
					complete: () => {
						console.log('关闭蓝牙模块');
						this.isBluetoothInit = false;
						this.isBluetoothAvailable = false;
					}
				});
			}
			
			// 清除超时定时器
			if (this.discoveryTimeout) {
				clearTimeout(this.discoveryTimeout);
				this.discoveryTimeout = null;
			}
		},
		goToCommandPage() {
			// 确保当前设备信息包含MAC地址
			const deviceInfo = {
				...this.currentDevice,
				macAddress: this.currentDevice?.macAddress || this.currentDevice?.deviceId
			};
			
			// 将当前连接的设备信息传递给指令下发页面
			uni.navigateTo({
				url: './commandForm?device=' + encodeURIComponent(JSON.stringify(deviceInfo))
			});
		},
		// 将创建连接逻辑拆分到单独的方法中
		createBLEConnection(device) {
			uni.createBLEConnection({
				deviceId: device.deviceId,
				timeout: 10000, // 10秒超时
				success: (res) => {
					console.log('连接蓝牙设备成功:', res);
					
					// 保存设备信息
					this.currentDevice = {
						...device,
						// 将MAC地址存储在设备信息中
						macAddress: device.macAddress || device.deviceId
					};
					
					// 获取设备的服务
					this.getBLEDeviceServices(device.deviceId);
					
					// 监听蓝牙连接状态
					this.onBLEConnectionStateChange();
					
					// 更新UI状态
					this.isConnected = true;
					this.bluetoothStatus.text = `已连接到: ${device.name}`;
					this.bluetoothStatus.class = 'connected';
					this.showDeviceList = false;
					
					this.showCustomToast({
						message: `成功连接到 ${device.name}`,
						type: 'default'
					});
				},
				fail: (err) => {
					console.error('连接蓝牙设备失败:', err);
					
					this.isConnected = false;
					this.currentDevice = null;
					this.bluetoothStatus.text = `连接 ${device.name} 失败，请重试`;
					this.bluetoothStatus.class = 'error';
					
					// 错误信息处理
					let errMsg = '';
					switch (err.errCode) {
						case 10003:
							errMsg = '连接超时，请确保设备在附近且已开启';
							break;
						case 10012:
							errMsg = '连接已建立';
							break;
						case 10013:
							errMsg = '连接失败，设备可能已关闭或超出范围';
							break;
						default:
							errMsg = err.errMsg || '连接失败，请重试';
					}
					
					this.showCustomToast({
						message: errMsg,
						type: 'error'
					});
				},
				complete: () => {
					this.isConnecting = false;
				}
			});
		},
		testDevice() {
			// 设备监测按钮点击处理
			if (!this.isConnected || !this.currentDevice) {
				this.showCustomToast({
					message: '请先连接设备',
					type: 'warning'
				});
				return;
			}
			
			// 获取存储的蓝牙写入服务和特征值ID
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				this.showCustomToast({
					message: '蓝牙特征值未找到，请重新连接设备',
					type: 'error'
				});
				return;
			}
			
			// 显示测试中提示
			this.showCustomToast({
				message: '正在测试设备...',
				type: 'default'
			});
			
			// 构建固定测试指令: a5 fe 1 0 0 1
			try {
				// 构建测试指令
				const buffer = new ArrayBuffer(6);
				const dataView = new DataView(buffer);
				
				// 填充命令数据
				dataView.setUint8(0, 0xA5); // 帧头1
				dataView.setUint8(1, 0xFE); // 帧头2
				dataView.setUint8(2, 0x01); // 命令
				dataView.setUint8(3, 0x00); // 数据长度高字节
				dataView.setUint8(4, 0x00); // 数据长度低字节
				dataView.setUint8(5, 0x01); // CRC校验
				
				// 记录测试指令
				const commandHex = 'A5 FE 01 00 00 01';
				console.log('发送测试指令:', commandHex);
				
				// 设置响应超时
				let responseTimeout;
				let testResult = false;
				let responseReceived = false;
				
				// 监听特征值变化，处理设备响应
				uni.onBLECharacteristicValueChange((res) => {
					if (responseReceived) return; // 已经收到响应，忽略后续数据
					
					if (res.value) {
						const buffer = res.value;
						const dataView = new DataView(buffer);
						const uint8Array = new Uint8Array(buffer);
						
						// 打印十六进制数据
						let hexData = '';
						for (let i = 0; i < uint8Array.length; i++) {
							hexData += uint8Array[i].toString(16).padStart(2, '0').toUpperCase() + ' ';
						}
						console.log('测试响应数据(HEX):', hexData);
						
						// 尝试解析为数字
						try {
							// 假设响应格式为4字节数据表示数字1234
							if (uint8Array.length >= 4) {
								// 检查是否为1234 (0x04D2)
								const responseValue = (uint8Array[0] << 24) | (uint8Array[1] << 16) | (uint8Array[2] << 8) | uint8Array[3];
								console.log('响应值:', responseValue);
								
								// 特定响应值1234表示设备正常（1234的ASCII码）825373492
								if (responseValue === 825373492) {
									testResult = true;
								}
							}
							
							responseReceived = true;
							clearTimeout(responseTimeout);
							this.processTestResult(testResult);
							
						} catch (error) {
							console.error('解析响应数据失败:', error);
						}
					}
				});
				
				// 写入测试指令到蓝牙设备
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: buffer,
					success: (res) => {
						console.log('发送测试指令成功:', res);
						
						// 设置响应超时，5秒后如果没收到响应则判断为测试失败
						responseTimeout = setTimeout(() => {
							if (!responseReceived) {
								console.log('测试响应超时');
								this.processTestResult(false);
							}
						}, 5000);
					},
					fail: (err) => {
						console.error('发送测试指令失败:', err);
						
						this.showCustomToast({
							message: '发送测试指令失败',
							type: 'error'
						});
					}
				});
				
			} catch (error) {
				console.error('准备测试指令时出错:', error);
				
				this.showCustomToast({
					message: '测试指令准备失败',
					type: 'error'
				});
			}
		},
		
		processTestResult(isNormal) {
			// 处理测试结果，记录到本地存储
			console.log(`设备测试结果: ${isNormal ? '正常' : '异常'}`);
			
			// 显示测试结果提示
			this.showCustomToast({
				message: `设备测试${isNormal ? '正常' : '异常'}`,
				type: isNormal ? 'default' : 'error'
			});
			
			// 获取当前设备MAC地址
			const macAddress = this.currentDevice?.macAddress || this.currentDevice?.deviceId || '';
			
			// 获取MAC地址与设备编号的映射关系
			let macMapping = {};
			try {
				const storedMapping = uni.getStorageSync('deviceMacMapping');
				if (storedMapping) {
					macMapping = JSON.parse(storedMapping);
				}
			} catch (e) {
				console.error('读取MAC映射失败:', e);
			}
			
			// 检查当前MAC地址是否已有分配的编号
			let deviceNumber;
			let isExistingDevice = false;
			
			if (macMapping[macAddress]) {
				// 已存在的设备，使用之前分配的编号
				deviceNumber = macMapping[macAddress];
				isExistingDevice = true;
			} else {
				// 新设备，分配新编号
				let deviceCounter = uni.getStorageSync('deviceTestCounter') || 0;
				deviceCounter++;
				deviceNumber = deviceCounter;
				
				// 更新设备编号计数器
				uni.setStorageSync('deviceTestCounter', deviceCounter);
				
				// 记录MAC地址与编号的映射关系
				macMapping[macAddress] = deviceNumber;
				uni.setStorageSync('deviceMacMapping', JSON.stringify(macMapping));
			}
			
			// 创建测试记录
			const testRecord = {
				id: Date.now(),
				deviceNumber: deviceNumber,
				macAddress: macAddress,
				isNormal: isNormal,
				command: 'A5 FE 01 00 00 01',
				timestamp: this.formatDateTime(new Date())
			};
			
			// 获取已有的测试记录
			let testRecords = [];
			try {
				const existingRecords = uni.getStorageSync('deviceTestRecords');
				if (existingRecords) {
					testRecords = JSON.parse(existingRecords);
				}
			} catch (e) {
				console.error('读取测试记录失败:', e);
			}
			
			if (isExistingDevice) {
				// 如果是已存在的设备，查找并更新其测试记录
				const recordIndex = testRecords.findIndex(r => r.macAddress === macAddress);
				if (recordIndex !== -1) {
					// 更新记录，保留原来的deviceNumber
					testRecords[recordIndex] = {
						...testRecord,
						deviceNumber: testRecords[recordIndex].deviceNumber
					};
				} else {
					// 虽然存在MAC映射，但没找到对应记录（可能被手动删除），添加新记录
					testRecords.push(testRecord);
				}
			} else {
				// 新设备，直接添加记录
				testRecords.push(testRecord);
			}
			
			// 保存更新后的记录
			uni.setStorageSync('deviceTestRecords', JSON.stringify(testRecords));
			
			// 提示用户可以查看测试记录
			setTimeout(() => {
				uni.showModal({
					title: '测试完成',
					content: `设备测试${isNormal ? '正常' : '异常'}，是否查看测试记录？`,
					confirmText: '查看记录',
					cancelText: '继续测试',
					success: (res) => {
						if (res.confirm) {
							// 跳转到测试记录页面
							this.goToTestRecordPage();
						}
					}
				});
			}, 1000);
		},
		goToTestRecordPage() {
			// 跳转到测试记录页面
			uni.navigateTo({
				url: './deviceTest'
			});
		},
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		}
	}
}
</script>

<style lang="scss" scoped>
.bluetooth-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
}

.status-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.status-text {
	text-align: center;
	color: #333333;
	margin-bottom: 30rpx;
	padding: 20rpx;
	border-radius: 10rpx;
	
	&.scanning {
		background-color: #e6f2ff;
		color: #47afff;
	}
	
	&.connected {
		background-color: #e6f9eb;
		color: #4cd964;
	}
	
	&.error {
		background-color: #ffebeb;
		color: #ff3b30;
	}
}

.scan-indicator {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 30rpx;
}

.loader {
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #47afff;
	border-radius: 50%;
	width: 40rpx;
	height: 40rpx;
	animation: spin 1s linear infinite;
	margin-right: 20rpx;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.btn {
	display: block;
	width: 100%;
	padding: 24rpx 0;
	font-size: 32rpx;
	font-weight: 500;
	border-radius: 16rpx;
	border: none;
	text-align: center;
	margin-bottom: 20rpx;
}

.btn-primary {
	background-color: #47afff;
	color: white;
}

.btn-secondary {
	background-color: #f5f5f5;
	color: #47afff;
	border: 2rpx solid #47afff;
}

.btn-success {
	background-color: #4cd964;
	color: white;
}

.btn-sm {
	padding: 16rpx 24rpx;
	font-size: 28rpx;
	width: auto;
}

.device-list {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.card-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 30rpx;
}

.empty-devices {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 60rpx 0;
	background-color: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.empty-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	color: #999999;
	text-align: center;
	font-size: 28rpx;
}

.device-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx 0;
	border-bottom: 2rpx solid #f5f5f5;
	
	&:last-child {
		border-bottom: none;
	}
}

.device-info {
	flex: 1;
}

.device-name {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 10rpx;
}

.device-mac {
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 10rpx;
}

.device-id {
	font-size: 24rpx;
	color: #999999;
}

.connect-btn {
	margin-bottom: 0;
}

.command-btn {
	margin-top: 30rpx;
}

.test-btn {
	margin-top: 30rpx;
}

.record-btn {
	margin-top: 30rpx;
}

.toast-container {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;
}

.toast-message {
	padding: 20rpx 40rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;
	
	&.error {
		background-color: rgba(255, 59, 48, 0.9);
	}
	
	&.warning {
		background-color: rgba(255, 204, 0, 0.9);
	}
}
</style> 