<template>
	<!-- 设备详情 -->
	<view class="devDetailInform">
		<view class="form_view rightForm">
			<u--form labelPosition="left" :model="modelPatient" ref="patientForm" labelWidth="120"
				:labelStyle="labelStyle">
				<u-form-item label="设备类型" prop="userInfo.equipTypeE" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.equipTypeE" maxlength="10" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="设备类型"></u--input>
						
				<!-- <textarea disabled   style="width: 100%;" class="textarea1"    auto-height >{{modelPatient.userInfo.equipTypeE}}</textarea> -->
				</u-form-item>
				<u-form-item label="设备编号" prop="userInfo.code" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.code" disabled disabledColor="#ffffff" inputAlign="right"
						border="none" placeholder="设备编号"></u--input>
				</u-form-item>
				<u-form-item label="设备名称"  prop="userInfo.name" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.name" maxlength="12" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="设备名称"></u--input>
				</u-form-item>
				<u-form-item label="设备状态" prop="userInfo.statusE" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.statusE" maxlength="10" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="设备状态"></u--input>
				</u-form-item>
				<u-form-item label="当前浓度(%LEL)" prop="userInfo.chroma" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.chroma" maxlength="10" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="当前浓度"></u--input>
				</u-form-item>
				<u-form-item label="当前温度(°C)" prop="userInfo.temp" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.temp" maxlength="10" disabled disabledColor="#ffffff"
						inputAlign="right" border="none" placeholder="当前温度"></u--input>
				</u-form-item>
				<u-form-item label="传感器信号强度" prop="userInfo.signal" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.signal" inputAlign="right" disabled disabledColor="#ffffff"
						border="none" placeholder="信号强度"></u--input>
				</u-form-item>
			<!-- 	<u-form-item label="设备电压" prop="userInfo.pwr" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.pwr" inputAlign="right" disabled disabledColor="#ffffff"
						border="none" placeholder="设备电压"></u--input>
				</u-form-item> -->
				<u-form-item  label="位置" labelWidth="180rpx" prop="userInfo.location" borderBottom :required="false">
					<!-- <u--input  v-model="modelPatient.userInfo.location" inputAlign="right" disabled
						disabledColor="#ffffff" border="none" placeholder="位置"></u--input> -->
						<view style="width: 100%; height: auto" >
							 <textarea style="text-align: right;width: 100%;"  placeholder-style="color: #c0c4cc;"  class="textarea1" placeholder="位置"  v-model="modelPatient.userInfo.location"  auto-height ></textarea>
						</view>
				</u-form-item>
				<u-form-item label="数据更新时间" prop="userInfo.updateTime" borderBottom :required="false">
					<u--input v-model="modelPatient.userInfo.updateTime" inputAlign="right" disabled
						disabledColor="#ffffff" border="none" placeholder="数据更新时间"></u--input>
				</u-form-item>
				<!-- <text class="annotClass">注：昨日6:00-今日6:00</text> -->
			</u--form>
		</view>
		<u-modal :content="content" title="提示" confirmText="确定" showCancelButton :show="showModal" @confirm="confirm"
			@cancel="showModal=false"></u-modal>
	</view>
</template>

<script>
	import {
		devDetail
	} from "@/network/api.js"
	export default {
		data() {
			return {
				content: '',
				showModal: false,
				labelStyle: {
					// fontSize: '28rpx',
					// fontFamily: 'Microsoft YaHei UI-Regular, Microsoft YaHei UI',
					// lineHeight: '28rpx'
				},
				modelPatient: {
					userInfo: {
						equipTypeE: '',
						code: '',
						name: '',
						statusE: '',
						chroma: '',
						temp: '',
						signal: '',
						pwr: '',
						location: '',
						updateTime: '',
						equipTypeEE: ''
					},
				}
				
			};
		},
		onLoad(opt) {
			this.init(opt)
		},
		methods: {
			init(opt) { // 初始化数据
			console.log(opt,'1');
				if(opt.isConnect == '1')
				{
				this.devicDetail(opt.code)
				}
				else{
					this.devicDetailEE(opt.code)
					this.modelPatient.userInfo.statusE = '设备离线'
				}
			},
			devicDetail(id) {
				let query = {
					code: id, //设备编码
					 token: uni.getStorageSync('token')
				}
				devDetail(query).then(res => {
						let resData = res.data;
						let equipType = '';
						switch (resData.equipType) {
							case '001':
								equipType = '家用型可燃气体探测器'
								break;
							case '002':
								equipType = '地下空间燃气泄漏监测仪'
								break;
							case '003':
								equipType = '工商业可燃气体探测器'
								break;
								case '004':
								equipType = '地埋式燃气泄漏监测仪'
								break;
							default:
								break;
						}
						resData.equipTypeE = equipType;
						// status  状态 00正常 01光路故障  02温度获取异常  03温控故障
						let status = '';
						switch (resData.status) {
							case '00':
								status = '正常工作'
								break;
							case '01':
								status = '光路故障'
								break;
							case '02':
								status = '温度获取异常'
								break;
							case '03':
								status = '温控故障'
								break;
							case '05':
								status = '传感器连接故障'
								break;
							default:
								break;
						}
						resData.statusE = resData.status;
						resData.location = resData.address;
						this.modelPatient.userInfo = res.data
					})
					.catch((err) => {})
			},
			devicDetailEE(id) {
				let query = {
					code: id, //设备编码
					 token: uni.getStorageSync('token')
				}
				devDetail(query).then(res => {
						let resData = res.data;
						let equipType = '';
						switch (resData.equipType) {
							case '001':
								equipType = '家用型可燃气体探测器'
								break;
							case '002':
								equipType = '地下空间燃气泄漏监测仪'
								break;
							case '003':
								equipType = '工商业可燃气体探测器'
								break;
								case '004':
								equipType = '地埋式燃气泄漏监测仪'
								break;
							default:
								break;
						}
						this.modelPatient.userInfo.equipTypeE = equipType
						console.log(this.modelPatient.userInfo.equipTypeE,'this.modelPatient.userInfo.equipTypeE')
						this.modelPatient.userInfo.code = resData.code
						this.modelPatient.userInfo.name = resData.name
						this.modelPatient.userInfo.location = resData.address
						this.modelPatient.userInfo.updateTime = resData.updateTime
						
						
						
					})
					.catch((err) => {})
			},
			confirm() {

			}
		}
	};
</script>

<style lang="scss" scoped>
	.devDetailInform {
		.twoBtn {
			display: flex;
		}

		.annotClass {
			font-size: 28rpx;
			font-family: Microsoft YaHei UI-Regular, Microsoft YaHei UI;
			color: rgb(192, 196, 204);
			line-height: 58rpx;
		}

		/deep/.u-modal__content__text {
			text-align: center;
		}
		.input-wrap {
		    white-space: pre-wrap;
		}
		.location {
			min-height: 30rpx;
			 width:100%;/*自动适应父布局宽度*/  
			 overflow:auto;  
			 word-break:break-all; 
			 
		}

	}
</style>