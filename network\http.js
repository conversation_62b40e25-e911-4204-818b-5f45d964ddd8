import {
	baseURL,
	baseURL_a
} from './base.js'; //导入接口的前缀地址

export const myRequest = (options) => {
	return new Promise((resolve, reject) => {
		uni.request({
			url: baseURL + options.url, //接口地址：前缀+方法中传入的地址
			method: options.method, //请求方法：传入的方法 或者默认是“GET”
			data: options.data || {}, //传递参数：传入的参数或者默认传递空集合
			timeout: 60000,
			header: {
				"Content-Type": options.method == 'GET' ?
					'application/x-www-form-urlencoded;charset=utf-8' : 'application/json',
				"Authorization": `${uni.getStorageSync('token')}` || '', //自定义请求头信息
			},
			success: (res) => {
				console.log("res", res.data)
				if (res.data.code != 200) {
					uni.showToast({
						title: res.data.msg || '请求失败',
						icon: "none"
					});
				} else if (res.data.code == 401) {
					uni.showToast({
						title: "登录过期，请重新登录",
						icon: 'none'
					})
					uni.clearStorage();
					setTimeout(() => {
						uni.reLaunch({
							url: `/pages/loginRegister/welcomePage`,
						});
					}, 1000)
				}
				else {
					//返回的数据（不固定，看后端接口，这里是做了一个判断，如果不为true，用uni.showToast方法提示获取数据失败)
					// if (res.data.success != true) {
					// 	return uni.showToast({
					// 		title: '获取数据失败',
					// 		icon: 'none'
					// 	})
					// }
					// 如果不满足上述判断就输出数据
					resolve(res.data)
				}
			},
			// 这里的接口请求，如果出现问题就输出接口请求失败
			fail: (err) => {
				console.log(err)
				uni.hideLoading();
				reject(err)
			}
		})
	})
}

export const myRequest_a = (options) => {
	return new Promise((resolve, reject) => {
		uni.request({
			url: baseURL_a + options.url, //接口地址：前缀+方法中传入的地址
			method: options.method, //请求方法：传入的方法 或者默认是“GET”
			data: options.data || {}, //传递参数：传入的参数或者默认传递空集合
			timeout: 60000,
			header: {
				"Content-Type": options.method == 'GET' ?
					'application/x-www-form-urlencoded;charset=utf-8' : 'application/json',
				"Authorization": `${uni.getStorageSync('token')}` || '', //自定义请求头信息
			},
			success: (res) => {
				console.log("res", res.data)
				if (res.data.code == 401) {
					uni.showToast({
						title: "登录过期，请重新登录",
						icon: 'none'
					})
					uni.clearStorage();
					setTimeout(() => {
						uni.reLaunch({
							url: `/pages/loginRegister/welcomePage`,
						});
					}, 1000)
				}
				else if(res.data.code == 200 || res.data.code == 500 || res.data.code == 1010 || res.data.code == 1011 || res.data.code == 1012 || res.data.code == 1013 || res.data.code == 1014){

					resolve(res.data)
				}
				else {
					uni.showToast({
						title: res.data.msg || '请求失败',
						icon: "none"
					});
				}
			},
			// 这里的接口请求，如果出现问题就输出接口请求失败
			fail: (err) => {
				console.log(err)
				uni.hideLoading();
				reject(err)
			}
		})
	})
}