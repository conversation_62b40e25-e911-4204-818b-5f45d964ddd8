<template>
	<view class="command-page">
		<view class="container">
			<view class="form-card">
				<view class="form-group">
					<view class="label">参数类型</view>
					<picker @change="paramTypeChange" :value="paramTypeIndex" :range="paramTypeArray" range-key="label">
						<view class="form-input">
							<text>{{ paramTypeText || '请选择参数类型' }}</text>
							<text class="picker-arrow">></text>
						</view>
					</picker>
				</view>
			</view>

			<view class="section-title">参数配置</view>

			<view class="form-card">
				<view class="form-group" v-if="paramType === '0x10'">
					<view class="label required">上报IP</view>
					<input class="form-input" type="text" v-model="reportIp" placeholder="请输入上报IP地址" />
				</view>

				<view class="form-group" v-if="paramType === '0x10'">
					<view class="label required">上报端口</view>
					<input class="form-input" type="number" v-model="reportPort" placeholder="请输入上报端口号" />
				</view>

				<view class="form-group" v-if="paramType === '0x11'">
					<view class="label required">采集频率(分钟)</view>
					<input class="form-input" type="number" v-model="collectFrequency" placeholder="请输入采集频率" />
				</view>

				<view class="form-group" v-if="paramType === '0x11'">
					<view class="label required">上报频率(分钟)</view>
					<input class="form-input" type="number" v-model="reportFrequency" placeholder="请输入上报频率" />
				</view>

				<view class="form-group" v-if="paramType === '0x12'">
					<view class="label required">高报阈值</view>
					<input class="form-input" type="number" v-model="highThreshold" placeholder="请输入高报阈值" />
				</view>

				<view class="form-group" v-if="paramType === '0x12'">
					<view class="label required">低报阈值</view>
					<input class="form-input" type="number" v-model="lowThreshold" placeholder="请输入低报阈值" />
				</view>

				<view class="form-group" v-if="paramType">
					<view class="label">指令预览</view>
					<view class="command-preview">{{ previewCommand || '完成参数填写后显示指令' }}</view>
				</view>
			</view>

			<view class="form-actions">
				<button class="btn btn-secondary" @click="resetForm">重置</button>
				<button class="btn btn-primary" @click="submitCommand">提交指令</button>
			</view>
		</view>

		<!-- 加载提示浮层 - 替换u-popup为普通弹窗 -->
		<view class="modal-overlay" v-if="showLoading">
			<view class="modal-content loading-modal">
				<view class="loading-icon"></view>
				<text class="loading-text">指令发送中...</text>
			</view>
		</view>

		<!-- 结果提示弹窗 - 替换u-modal为普通弹窗 -->
		<view class="modal-overlay" v-if="showResult">
			<view class="modal-content result-modal">
				<view class="result-icon" :class="resultData.success ? 'success' : 'error'">
					{{ resultData.success ? '✓' : '✕' }}
				</view>
				<view class="result-title">{{ resultData.title }}</view>
				<view class="result-message">{{ resultData.content }}</view>
				<button class="btn btn-primary confirm-btn" @click="handleResultConfirm">知道了</button>
			</view>
		</view>

		<!-- 替换u-toast为简单的自定义toast -->
		<view class="toast-container" v-if="showToast">
			<view class="toast-message" :class="toastType">{{ toastMessage }}</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			currentDevice: null,
			reportIp: '',
			reportPort: '',
			collectFrequency: '',
			reportFrequency: '',
			highThreshold: '',
			lowThreshold: '',

			showLoading: false,
			showResult: false,

			resultData: {
				title: '',
				content: '',
				success: false
			},

			commandHistory: [], // 用于存储命令历史

			// 添加toast相关变量
			showToast: false,
			toastMessage: '',
			toastType: 'default',

			paramType: '',
			paramTypeText: '',
			paramTypeIndex: 0,
			paramTypeArray: [
				{
					value: '0x10',
					label: '设备网络参数配置'
				},
				{
					value: '0x11',
					label: '设备监测频率设置'
				},
				{
					value: '0x12',
					label: '设备报警阈值设置'
				}
			],
		}
	},
	computed: {
		// 计算指令预览
		previewCommand() {
			if (!this.paramType) return '';
			
			try {
				let cmd = 0;
				let dataBytes = [];
				let dataLength = 0;
				
				// 确定命令类型和数据
				if (this.paramType === '0x10') { // IP端口配置
					if (!this.reportIp || !this.reportPort) return '';
					
					cmd = 0x10;
					
					// 处理IP地址，按"."分割为4个数
					const ipParts = this.reportIp.split('.');
					if (ipParts.length !== 4) return '';
					
					const ipNumbers = ipParts.map(part => parseInt(part));
					if (ipNumbers.some(isNaN)) return '';
					
					// 处理端口号
					const port = parseInt(this.reportPort);
					if (isNaN(port)) return '';
					
					const portHigh = (port >> 8) & 0xFF; // 高位字节
					const portLow = port & 0xFF; // 低位字节
					
					// 组装数据部分
					if (port < 257) {
						// 端口号小于257时，只需要1个字节存储
						dataBytes = [...ipNumbers, portLow];
						dataLength = 5; // IP地址(4字节) + 端口(1字节) = 5字节
					} else {
						// 端口号大于等于257时，需要2个字节存储
						dataBytes = [...ipNumbers, portHigh, portLow];
						dataLength = 6; // IP地址(4字节) + 端口(2字节) = 6字节
					}
				} else if (this.paramType === '0x11') { // 采集频率和上报频率设置
					if (!this.collectFrequency || !this.reportFrequency) return '';
					
					const collectFreq = parseInt(this.collectFrequency);
					const reportFreq = parseInt(this.reportFrequency);
					
					if (isNaN(collectFreq) || isNaN(reportFreq)) return '';
					
					cmd = 0x11;
					dataBytes = [collectFreq, reportFreq];
					dataLength = 2; // 采集频率(1字节) + 上报频率(1字节) = 2字节
				} else if (this.paramType === '0x12') { // 报警阈值设置
					if (!this.highThreshold || !this.lowThreshold) return '';
					
					const highThresh = parseInt(this.highThreshold);
					const lowThresh = parseInt(this.lowThreshold);
					
					if (isNaN(highThresh) || isNaN(lowThresh)) return '';
					
					cmd = 0x12;
					dataBytes = [highThresh, lowThresh];
					dataLength = 2; // 高报阈值(1字节) + 低报阈值(1字节) = 2字节
				}
				
				// 计算CRC校验 - 命令 + 数据长度 + 数据的和取低位
				let crc = cmd;
				crc += (dataLength >> 8) & 0xFF;
				crc += dataLength & 0xFF;
				
				for (let i = 0; i < dataBytes.length; i++) {
					crc += dataBytes[i];
				}
				
				crc = crc & 0xFF; // 取低8位
				
				// 构建指令预览字符串
				let preview = 'A5FE';  // 帧头
				preview += cmd.toString(16).padStart(2, '0').toUpperCase();  // 命令
				preview += dataLength.toString(16).padStart(4, '0').toUpperCase();  // 数据长度
				dataBytes.forEach(byte => {
					preview += byte.toString(16).padStart(2, '0').toUpperCase();  // 数据
				});
				preview += crc.toString(16).padStart(2, '0').toUpperCase();  // CRC
				
				return preview;
			} catch (e) {
				console.error('生成指令预览失败:', e);
				return '';
			}
		}
	},
	onLoad(options) {
		// 获取从蓝牙连接页面传递过来的设备信息
		if (options.device) {
			try {
				this.currentDevice = JSON.parse(decodeURIComponent(options.device));
			} catch (e) {
				console.error('解析设备信息失败:', e);
			}
		}

		// 设置页面标题，显示当前设备名称
		if (this.currentDevice && this.currentDevice.name) {
			uni.setNavigationBarTitle({
				title: '指令下发 - ' + this.currentDevice.name
			})
		}

		// 从本地存储加载历史记录
		const history = uni.getStorageSync('commandHistory');
		if (history) {
			try {
				this.commandHistory = JSON.parse(history);
			} catch (e) {
				console.error('解析历史记录失败:', e);
			}
		}
	},
	methods: {
		resetForm() {
			this.paramType = '';
			this.paramTypeText = '';
			this.paramTypeIndex = 0;
			this.reportIp = '';
			this.reportPort = '';
			this.collectFrequency = '';
			this.reportFrequency = '';
			this.highThreshold = '';
			this.lowThreshold = '';
		},
		// 自定义toast方法替代u-toast
		showCustomToast(options) {
			this.toastMessage = options.message || '';
			this.toastType = options.type || 'default';
			this.showToast = true;

			// 自动关闭
			setTimeout(() => {
				this.showToast = false;
			}, 2000);

			// 为了兼容性，同时使用uni.showToast
			uni.showToast({
				title: options.message || '',
				icon: options.type === 'error' ? 'error' : (options.type === 'warning' ? 'none' : 'success'),
				duration: 2000
			});
		},
		submitCommand() {
			// 表单验证
			if (!this.paramType) {
				this.showCustomToast({
					message: '请选择参数类型',
					type: 'error'
				});
				return;
			}

			if (this.paramType === '0x10') {
				if (!this.reportIp) {
					this.showCustomToast({
						message: '请输入上报IP',
						type: 'error'
					});
					return;
				}

				if (!this.reportPort) {
					this.showCustomToast({
						message: '请输入上报端口',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x11') {
				if (!this.collectFrequency) {
					this.showCustomToast({
						message: '请输入采集频率',
						type: 'error'
					});
					return;
				}
				
				if (!this.reportFrequency) {
					this.showCustomToast({
						message: '请输入上报频率',
						type: 'error'
					});
					return;
				}
			} else if (this.paramType === '0x12') {
				if (!this.highThreshold) {
					this.showCustomToast({
						message: '请输入高报阈值',
						type: 'error'
					});
					return;
				}
				
				if (!this.lowThreshold) {
					this.showCustomToast({
						message: '请输入低报阈值',
						type: 'error'
					});
					return;
				}
			}

			// 显示加载中
			this.showLoading = true;

			// 组装要发送的命令数据
			const commandData = {
				paramType: this.paramType,
				reportIp: this.reportIp,
				reportPort: this.reportPort,
				collectFrequency: this.collectFrequency,
				reportFrequency: this.reportFrequency,
				highThreshold: this.highThreshold,
				lowThreshold: this.lowThreshold,
			};

			console.log('准备发送指令:', commandData);

			// 获取存储的蓝牙写入服务和特征值ID
			const deviceId = this.currentDevice?.deviceId;
			const serviceId = uni.getStorageSync('writeServiceId');
			const characteristicId = uni.getStorageSync('writeCharacteristicId');
			
			if (!deviceId || !serviceId || !characteristicId) {
				this.showLoading = false;
				this.resultData = {
					title: '下发失败',
					content: '蓝牙特征值未找到，请重新连接设备',
					success: false
				};
				this.showResult = true;
				return;
			}

			// 构建发送数据
			try {
				// 根据蓝牙协议格式构建数据
				/*
				帧头      命令   数据长度     数据       CRC     
				2Byte    1Byte   2Byte      n Byte     1Byte  
				0x5AFE    xx      xxxx        xx         xx    
				*/
				let cmd = 0;
				let dataBytes = [];
				let dataLength = 0;
				
				// 确定命令类型和数据
				if (this.paramType === '0x10') { // IP端口配置
					cmd = 0x10;
					
					// 处理IP地址，按"."分割为4个数
					const ipParts = this.reportIp.split('.').map(part => parseInt(part));
					
					// 处理端口号
					const port = parseInt(this.reportPort);
					
					const portHigh = (port >> 8) & 0xFF; // 高位字节
					const portLow = port & 0xFF; // 低位字节
					
					// 组装数据部分
					if (port < 257) {
						// 端口号小于257时，只需要1个字节存储
						dataBytes = [...ipParts, portLow];
						dataLength = 5; // IP地址(4字节) + 端口(1字节) = 5字节
					} else {
						// 端口号大于等于257时，需要2个字节存储
						dataBytes = [...ipParts, portHigh, portLow];
						dataLength = 6; // IP地址(4字节) + 端口(2字节) = 6字节
					}
				} else if (this.paramType === '0x11') { // 采集频率和上报频率设置
					cmd = 0x11;
					dataBytes = [parseInt(this.collectFrequency), parseInt(this.reportFrequency)];
					dataLength = 2; // 采集频率(1字节) + 上报频率(1字节) = 2字节
				} else if (this.paramType === '0x12') { // 报警阈值设置
					cmd = 0x12;
					dataBytes = [parseInt(this.highThreshold), parseInt(this.lowThreshold)];
					dataLength = 2; // 高报阈值(1字节) + 低报阈值(1字节) = 2字节
				}
				
				// 计算CRC校验 - 命令 + 数据长度 + 数据的和取低位
				let crc = cmd;
				crc += (dataLength >> 8) & 0xFF;
				crc += dataLength & 0xFF;
				
				for (let i = 0; i < dataBytes.length; i++) {
					crc += dataBytes[i];
				}
				
				crc = crc & 0xFF; // 取低8位
				
				// 构建完整的蓝牙指令
				// 帧头(2字节) + 命令(1字节) + 数据长度(2字节) + 数据(n字节) + CRC(1字节)
				const totalLength = 2 + 1 + 2 + dataLength + 1;
				const buffer = new ArrayBuffer(totalLength);
				const dataView = new DataView(buffer);
				
				// 填充帧头 (0xA5FE)
				dataView.setUint8(0, 0xA5);
				dataView.setUint8(1, 0xFE);
				
				// 填充命令
				dataView.setUint8(2, cmd);
				
				// 填充数据长度 (大端序)
				dataView.setUint16(3, dataLength, false);
				
				// 填充数据
				for (let i = 0; i < dataBytes.length; i++) {
					dataView.setUint8(5 + i, dataBytes[i]);
				}
				
				// 填充CRC校验
				dataView.setUint8(totalLength - 1, crc);
				
				// 记录日志 - 指令预览
				const commandHexString = this.arrayBufferToHexString(buffer);
				console.log('发送的蓝牙指令:', commandHexString);

				// 写入数据到蓝牙设备
				uni.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: buffer,
					success: (res) => {
						console.log('写入蓝牙特征值成功:', res);

						this.showLoading = false;

						// 指令发送成功
						this.resultData = {
							title: '下发成功',
							content: `指令已成功发送：${commandHexString}`,
							success: true
						};

						// 保存到历史记录
						const record = {
							id: Date.now(),
							paramType: this.paramTypeText,
							reportIp: this.reportIp,
							reportPort: this.reportPort,
							collectFrequency: this.collectFrequency,
							reportFrequency: this.reportFrequency,
							highThreshold: this.highThreshold,
							lowThreshold: this.lowThreshold,
							command: commandHexString,
							timestamp: this.formatDateTime(new Date()),
							status: 'pending_sync' // 初始状态为待同步
						};

						this.commandHistory.push(record);

						// 保存到本地存储
						uni.setStorageSync('commandHistory', JSON.stringify(this.commandHistory));

						// 同步到后端
						this.syncToBackend(record);

						this.showResult = true;
					},
					fail: (err) => {
						console.error('写入蓝牙特征值失败:', err);

						this.showLoading = false;

						// 指令发送失败
						this.resultData = {
							title: '下发失败',
							content: '指令发送失败，请检查设备连接或参数后重试。错误：' + (err.errMsg || '未知错误'),
							success: false
						};

						this.showResult = true;
					}
				});
			} catch (error) {
				console.error('准备发送数据时出错:', error);

				this.showLoading = false;

				// 指令发送失败
				this.resultData = {
					title: '下发失败',
					content: '指令数据准备失败：' + error.message,
					success: false
				};

				this.showResult = true;
			}
		},
		// 辅助方法：将ArrayBuffer转换为16进制字符串（用于调试）
		arrayBufferToHexString(buffer) {
			const uint8Array = new Uint8Array(buffer);
			return Array.from(uint8Array)
				.map(byte => byte.toString(16).padStart(2, '0').toUpperCase())
				.join(' ');
		},
		handleResultConfirm() {
			this.showResult = false;
			// 如果成功，可以考虑返回到上一页
			if (this.resultData.success) {
				// uni.navigateBack();
			}
		},
		syncToBackend(record) {
			console.log('同步记录到后端:', record);

			// TODO: 实现与后端的同步逻辑，调用API
			setTimeout(() => {
				// 模拟同步结果，70%的成功率
				const syncSuccess = Math.random() > 0.3;

				// 更新记录状态
				const index = this.commandHistory.findIndex(r => r.id === record.id);
				if (index !== -1) {
					this.commandHistory[index].status = syncSuccess ? 'synced' : 'sync_failed';

					// 更新本地存储
					uni.setStorageSync('commandHistory', JSON.stringify(this.commandHistory));

					console.log(`记录 ${record.id} 同步${syncSuccess ? '成功' : '失败'}`);

					// 如果同步失败，可以考虑重试机制
					if (!syncSuccess) {
						// 稍后重试
						// this.retrySync(record, 3000);
					}
				}
			}, 3000);
		},
		retrySync(record, delay) {
			console.log(`${delay / 1000}秒后重试同步记录:`, record);

			setTimeout(() => {
				this.syncToBackend(record);
			}, delay);
		},
		paramTypeChange(e) {
			const index = e.detail.value;
			this.paramTypeIndex = index;
			this.paramType = this.paramTypeArray[index].value;
			this.paramTypeText = this.paramTypeArray[index].label;
		},
		formatDateTime(date) {
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			const seconds = String(date.getSeconds()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
		}
	}
}
</script>

<style lang="scss" scoped>
.command-page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

.container {
	padding: 30rpx;
}

.form-card {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	font-size: 28rpx;
	color: #666666;
	margin: 30rpx 0 20rpx;
	padding-left: 20rpx;
	position: relative;

	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 28rpx;
		background-color: #47afff;
		border-radius: 3rpx;
	}
}

.form-group {
	margin-bottom: 30rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.label {
	font-size: 28rpx;
	color: #333333;
	margin-bottom: 16rpx;

	&.required::before {
		content: '*';
		color: #ff3b30;
		margin-right: 8rpx;
	}
}

.form-input {
	width: 100%;
	height: 88rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 16rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-sizing: border-box;

	&.disabled {
		background-color: #f5f5f5;
		color: #999999;
	}
}

.command-preview {
	width: 100%;
	min-height: 88rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	font-size: 28rpx;
	font-family: monospace;
	background-color: #f9f9f9;
	color: #333;
	word-break: break-all;
	display: flex;
	align-items: center;
}

.form-actions {
	display: flex;
	justify-content: space-between;
	margin-top: 60rpx;
	gap: 30rpx;
}

.btn {
	flex: 1;
	height: 88rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 500;
}

.btn-primary {
	background-color: #47afff;
	color: white;
}

.btn-secondary {
	background-color: #f5f5f5;
	color: #666666;
	border: 2rpx solid #e0e0e0;
}

/* 自定义modal样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.modal-content {
	background-color: #ffffff;
	border-radius: 20rpx;
	padding: 40rpx;
	width: 560rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.loading-modal {
	width: 240rpx;
	height: 240rpx;
}

.loading-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	border: 6rpx solid #f3f3f3;
	border-top: 6rpx solid #47afff;
	animation: spin 1s linear infinite;
	margin-bottom: 30rpx;
}

.result-icon {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 60rpx;
	font-weight: bold;
	margin-bottom: 30rpx;

	&.success {
		background-color: #e6f9eb;
		color: #4cd964;
	}

	&.error {
		background-color: #ffebeb;
		color: #ff3b30;
	}
}

.result-title {
	font-size: 36rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.result-message {
	font-size: 28rpx;
	color: #666666;
	text-align: center;
	margin-bottom: 40rpx;
}

.confirm-btn {
	width: 100%;
}

/* 自定义toast样式 */
.toast-container {
	position: fixed;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 9999;
}

.toast-message {
	padding: 20rpx 40rpx;
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 10rpx;
	font-size: 28rpx;

	&.error {
		background-color: rgba(255, 59, 48, 0.9);
	}

	&.warning {
		background-color: rgba(255, 204, 0, 0.9);
	}
}

.picker-arrow {
	color: #999;
	font-size: 32rpx;
}

.form-message {
	padding: 30rpx;
	background-color: #f5f5f5;
	border-radius: 16rpx;
	text-align: center;
	color: #666666;
}
</style>